package cn.gwssi;

import cn.gwssi.util.Pool;
import org.nustaq.serialization.FSTConfiguration;

/**
 * Created by duq on 2019/3/20.
 */
public abstract class FSTConfigurationFactory {

    private boolean threadSafe = false;

    private boolean softReferences = false;

    private int maximumCapacity = 10;

    protected FSTConfigurationFactory(){

    }

    public void addThreadSafe(boolean threadSafe) {
        this.threadSafe = threadSafe;
    }

    public void addSoftReferences(boolean softReferences) {
        this.softReferences = softReferences;
    }

    public void addMaximumCapacity(int maximumCapacity) {
        this.maximumCapacity = maximumCapacity;
    }

    public boolean getThreadSafe() {
        return threadSafe;
    }


    public boolean getSoftReferences() {
        return softReferences;
    }


    public int getMaximumCapacity() {
        return maximumCapacity;
    }

    abstract <T> T setThreadSafe(boolean threadSafe);

    abstract <T> T  setSoftReferences(boolean softReferences);

    abstract  <T> T setMaximunCapacity(int maximumCapacity);

    abstract <T> T build();

    public Pool<FSTConfiguration> getKryoPool(){
        Pool<FSTConfiguration> kryoPool = new Pool<FSTConfiguration>(threadSafe,softReferences, maximumCapacity) {
            @Override
            protected FSTConfiguration create() {
                FSTConfiguration configuration= FSTConfiguration.createStructConfiguration();
                configuration.setStructMode(true);
                return configuration;
            }
        };
        return kryoPool;
    }

}
